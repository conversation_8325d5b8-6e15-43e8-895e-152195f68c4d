<div class="inventory-overview-container">
  <h2>Inventory Overview</h2>
  <div class="content">
    <!-- Modern Hero Section -->
    <div class="hero-section" *ngIf="inventorySummary">

      <!-- Enhanced Summary Cards -->
      <div class="summary-grid">
        <!-- Total Value Card -->
        <div class="modern-summary-card value-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="card-trend positive">
                <mat-icon>trending_up</mat-icon>
                <span>+2.5%</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ formatCurrency(inventorySummary.totalValue) }}</div>
              <div class="card-label">Total Inventory Value</div>
              <div class="card-description">Across all note series</div>
            </div>
          </div>
        </div>

        <!-- Total Notes Card -->
        <div class="modern-summary-card notes-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>receipt_long</mat-icon>
              </div>
              <div class="card-trend neutral">
                <mat-icon>remove</mat-icon>
                <span>0%</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ formatNumber(inventorySummary.totalNotes) }}</div>
              <div class="card-label">Total Notes</div>
              <div class="card-description">Physical bank notes</div>
            </div>
          </div>
        </div>

        <!-- Low Stock Alerts Card -->
        <div class="modern-summary-card alerts-card" [class.critical]="inventorySummary.lowStockAlerts.length > 5">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>warning</mat-icon>
                <div class="alert-badge" *ngIf="inventorySummary.lowStockAlerts.length > 0">!</div>
              </div>
              <div class="card-trend" [class.warning]="inventorySummary.lowStockAlerts.length > 0">
                <mat-icon>priority_high</mat-icon>
                <span>{{ inventorySummary.lowStockAlerts.length > 0 ? 'Alert' : 'OK' }}</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ inventorySummary.lowStockAlerts.length }}</div>
              <div class="card-label">Low Stock Alerts</div>
              <div class="card-description">Require attention</div>
            </div>
          </div>
        </div>

        <!-- Series Count Card -->
        <div class="modern-summary-card series-card">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon">
                <mat-icon>category</mat-icon>
              </div>
              <div class="card-trend positive">
                <mat-icon>check_circle</mat-icon>
                <span>Active</span>
              </div>
            </div>
            <div class="card-body">
              <div class="primary-value">{{ getSeriesArray().length }}</div>
              <div class="card-label">Note Series</div>
              <div class="card-description">Available types</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Inventory Section - Redesigned -->
    <div class="inventory-section">
      <div class="inventory-container">
        <!-- Enhanced Section Header with Gradient Background -->
        <div class="section-header-redesigned">
          <div class="header-background">
            <div class="gradient-overlay"></div>
            <div class="pattern-overlay"></div>
          </div>
          <div class="header-content">
            <div class="header-text">
              <h2 class="section-title-redesigned">
                <mat-icon class="title-icon">inventory_2</mat-icon>
                Detailed Inventory
              </h2>
              <p class="section-subtitle-redesigned">Comprehensive cash management by note series</p>
            </div>
            <div class="header-actions">
              <button mat-fab color="primary" class="floating-action-btn" *ngIf="userService.hasManagerPrivileges()">
                <mat-icon>add</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- Redesigned Tab Navigation -->
        <div class="inventory-content">
          <div class="series-navigation">
            <mat-tab-group class="creative-tabs" color="primary" animationDuration="300ms">
              <mat-tab *ngFor="let series of getSeriesArray()" [label]="NOTE_SERIES_LABELS[series]">
                <ng-template mat-tab-label>
                  <div class="tab-label-content">
                    <mat-icon class="tab-icon">{{ getSeriesIcon(series) }}</mat-icon>
                    <span class="tab-text">{{ NOTE_SERIES_LABELS[series] }}</span>
                    <div class="tab-indicator" [class]="'indicator-' + series.toLowerCase()"></div>
                  </div>
                </ng-template>

                <div class="tab-content-redesigned">
                  <!-- Enhanced Series Overview with Visual Cards -->
                  <div class="series-overview-redesigned">
                    <div class="overview-cards-grid">
                      <!-- Total Notes Card -->
                      <div class="overview-card notes-card" [class]="'card-' + series.toLowerCase()">
                        <div class="card-decoration">
                          <div class="decoration-circle"></div>
                          <div class="decoration-dots"></div>
                        </div>
                        <div class="card-icon-container">
                          <div class="icon-background">
                            <mat-icon class="card-icon">receipt_long</mat-icon>
                          </div>
                        </div>
                        <div class="card-content">
                          <div class="card-value">{{ formatSeriesQuantityDisplay(series) }}</div>
                          <div class="card-label">TOTAL NOTES</div>
                          <div class="card-trend">
                            <mat-icon class="trend-icon">trending_up</mat-icon>
                            <span class="trend-text">+5.2%</span>
                          </div>
                        </div>
                      </div>

                      <!-- Total Value Card -->
                      <div class="overview-card value-card" [class]="'card-' + series.toLowerCase()">
                        <div class="card-decoration">
                          <div class="decoration-circle"></div>
                          <div class="decoration-dots"></div>
                        </div>
                        <div class="card-icon-container">
                          <div class="icon-background">
                            <mat-icon class="card-icon">account_balance_wallet</mat-icon>
                          </div>
                        </div>
                        <div class="card-content">
                          <div class="card-value">{{ formatCurrency(getSeriesTotal(series).value) }}</div>
                          <div class="card-label">TOTAL VALUE</div>
                          <div class="card-trend">
                            <mat-icon class="trend-icon">trending_up</mat-icon>
                            <span class="trend-text">+2.8%</span>
                          </div>
                        </div>
                      </div>

                      <!-- Stock Status Card -->
                      <div class="overview-card status-card" [class]="'card-' + series.toLowerCase()">
                        <div class="card-decoration">
                          <div class="decoration-circle"></div>
                          <div class="decoration-dots"></div>
                        </div>
                        <div class="card-icon-container">
                          <div class="icon-background">
                            <mat-icon class="card-icon">assessment</mat-icon>
                          </div>
                        </div>
                        <div class="card-content">
                          <div class="card-value">{{ getSeriesStockStatus(series) }}</div>
                          <div class="card-label">STOCK STATUS</div>
                          <div class="status-indicators">
                            <div class="status-dot good"></div>
                            <div class="status-dot warning"></div>
                            <div class="status-dot critical"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Redesigned Inventory Grid with Enhanced Cards -->
                  <div class="inventory-grid-redesigned">
                    <div class="denomination-card"
                         *ngFor="let item of inventoryBreakdown[series]; trackBy: trackByDenomination"
                         [class]="'denomination-' + item.denomination + ' series-' + series.toLowerCase()"
                         [@cardAnimation]>

                      <!-- Card Header with Note Image and Status -->
                      <div class="card-header-redesigned">
                        <div class="note-display">
                          <div class="note-image-container">
                            <img [src]="getDenominationImage(item.denomination, series)"
                                 [alt]="getDenominationLabel(item.denomination) + ' - ' + NOTE_SERIES_LABELS[series]"
                                 class="note-image-redesigned"
                                 loading="lazy"
                                 (error)="onImageError($event)">
                            <div class="image-overlay"></div>
                            <div class="denomination-badge">{{ getDenominationLabel(item.denomination) }}</div>
                          </div>
                          <div class="note-info">
                            <h3 class="note-title">{{ getDenominationLabel(item.denomination) }}</h3>
                            <p class="note-series">{{ NOTE_SERIES_LABELS[series] }}</p>
                            <div class="note-value">{{ formatCurrency(item.denomination) }}</div>
                          </div>
                        </div>

                        <div class="status-section">
                          <div class="status-chip" [class]="getStockStatus(item).class">
                            <mat-icon class="status-icon">{{ getStockStatusIcon(item) }}</mat-icon>
                            <span class="status-text">{{ getStockStatus(item).status }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Enhanced Metrics Section -->
                      <div class="metrics-section-redesigned">
                        <div class="metrics-grid">
                          <div class="metric-card quantity-metric">
                            <div class="metric-icon">
                              <mat-icon>inventory</mat-icon>
                            </div>
                            <div class="metric-data">
                              <div class="metric-value-redesigned">{{ formatQuantityDisplay(item.quantity) }}</div>
                              <div class="metric-label-redesigned">Quantity Available</div>
                            </div>
                          </div>

                          <div class="metric-card value-metric">
                            <div class="metric-icon">
                              <mat-icon>monetization_on</mat-icon>
                            </div>
                            <div class="metric-data">
                              <div class="metric-value-redesigned">{{ formatCurrency(item.value) }}</div>
                              <div class="metric-label-redesigned">Total Value</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Interactive Stock Level Indicator -->
                      <div class="stock-level-section">
                        <div class="stock-header">
                          <span class="stock-label">Stock Level</span>
                          <span class="stock-percentage">{{ getStockPercentage(item) }}%</span>
                        </div>
                        <div class="stock-progress-redesigned">
                          <div class="progress-track">
                            <div class="progress-fill"
                                 [style.width.%]="getStockPercentage(item)"
                                 [class]="getStockProgressColor(item)">
                              <div class="progress-glow"></div>
                            </div>
                          </div>
                          <div class="stock-indicators">
                            <div class="indicator low" [class.active]="getStockPercentage(item) <= 25"></div>
                            <div class="indicator medium" [class.active]="getStockPercentage(item) > 25 && getStockPercentage(item) <= 75"></div>
                            <div class="indicator high" [class.active]="getStockPercentage(item) > 75"></div>
                          </div>
                        </div>
                      </div>

                      <!-- Action Buttons -->
                      <div class="card-actions-redesigned" *ngIf="userService.hasManagerPrivileges()">
                        <button mat-raised-button
                                class="action-btn primary-action"
                                (click)="onAddCash(item.noteSeries, item.denomination)">
                          <mat-icon>add_circle</mat-icon>
                          <span>Add Cash</span>
                        </button>
                        <button mat-stroked-button
                                class="action-btn secondary-action"
                                (click)="onViewDetails(item)">
                          <mat-icon>visibility</mat-icon>
                          <span>Details</span>
                        </button>
                      </div>
                    </div>
                  </div>

                <!-- No Data State -->
                <div class="no-data" *ngIf="inventoryBreakdown[series].length === 0">
                  <div class="no-data-icon">
                    <mat-icon>inventory_2</mat-icon>
                  </div>
                  <h3>No Inventory Data</h3>
                  <p>No inventory data available for {{ NOTE_SERIES_LABELS[series] }}</p>
                  <button mat-raised-button color="primary" (click)="onAddCash(series)" *ngIf="userService.hasManagerPrivileges()">
                    <mat-icon>add_circle</mat-icon>
                    Add Initial Stock
                  </button>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
</div>
