.inventory-overview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  h2 {
    margin: 0 0 30px 0;
    color: #1a365d;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    padding: 2rem 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(135deg, #CC0000 0%, #FF6B6B 100%);
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
      padding: 1.5rem 0 0.75rem 0;
    }

    @media (max-width: 480px) {
      font-size: 1.75rem;
      padding: 1rem 0 0.5rem 0;
    }
  }

  .content {
    padding: 1rem 2rem 0.5rem 2rem;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
    background: transparent;

    // Large screens - utilize more space
    @media (min-width: 1600px) {
      max-width: 95%;
      padding: 1rem 3rem 0.5rem 3rem;
    }

    @media (min-width: 1200px) and (max-width: 1599px) {
      max-width: 1600px;
      padding: 1rem 2.5rem 0.5rem 2.5rem;
    }

    @media (min-width: 900px) and (max-width: 1199px) {
      max-width: 1200px;
      padding: 1rem 2rem 0.5rem 2rem;
    }

    @media (max-width: 768px) {
      padding: 1rem 1rem 0.5rem 1rem;
    }
  }
}

// Modern Hero Section
.hero-section {
  margin-bottom: 2rem;
}

// Modern Summary Grid
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 4 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Summary Cards
.modern-summary-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0,0,0,0.15);
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.08;
    transform: translate(40px, -40px);
  }

  .card-content {
    position: relative;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      mat-icon {
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        color: white;
      }

      .alert-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 20px;
        height: 20px;
        background-color: #CC0000;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 700;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;

      &.positive {
        background: rgba(76, 175, 80, 0.1);
        color: #4CAF50;
      }

      &.neutral {
        background: rgba(158, 158, 158, 0.1);
        color: #9E9E9E;
      }

      &.warning {
        background: rgba(255, 152, 0, 0.1);
        color: #FF9800;
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }

  .card-body {
    .primary-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1a365d;
      line-height: 1;
      margin-bottom: 0.5rem;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .card-label {
      font-size: 1rem;
      font-weight: 600;
      color: #1a365d;
      margin-bottom: 0.25rem;
    }

    .card-description {
      font-size: 0.875rem;
      color: #718096;
      font-weight: 500;
    }
  }

  // Card-specific styling
  &.value-card {
    .card-background {
      background: #4CAF50;
    }
    .card-icon {
      background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: #2196F3;
    }
    .card-icon {
      background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    }
  }

  &.alerts-card {
    .card-background {
      background: #FFC107;
    }
    .card-icon {
      background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%);
    }

    &.critical {
      .card-background {
        background: #f44336;
      }
      .card-icon {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      }
    }
  }

  &.series-card {
    .card-background {
      background: #1a365d;
    }
    .card-icon {
      background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    }
  }
}

// Modern Inventory Section
.inventory-section {
  .inventory-container {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;

    // Add subtle gradient overlay
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 200px;
      background: linear-gradient(135deg, rgba(26, 54, 93, 0.02) 0%, rgba(33, 150, 243, 0.02) 100%);
      pointer-events: none;
      z-index: 1;
    }
  }

  .section-header {
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    color: white;
    padding: 4rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 200px;

    // Animated gradient background
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;

    // Add floating particles effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.08) 1.5px, transparent 1.5px);
      background-size: 50px 50px, 30px 30px, 70px 70px;
      animation: floatParticles 15s linear infinite;
      opacity: 0.6;
    }

    // Add glowing orb effect
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      transform: translate(-50%, -50%);
      animation: pulse 4s ease-in-out infinite;
      border-radius: 50%;
    }

    @media (max-width: 768px) {
      padding: 3rem 1.5rem;
      min-height: 160px;
    }

    .section-title {
      font-size: 3rem;
      font-weight: 900;
      margin: 0 0 1rem 0;
      color: white !important;
      letter-spacing: -1px;
      position: relative;
      z-index: 3;
      text-shadow: 0 4px 20px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ffffff 0%, #f0f8ff 50%, #ffffff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-size: 200% 200%;
      animation: textShimmer 3s ease-in-out infinite;

      @media (max-width: 768px) {
        font-size: 2.2rem;
        margin-bottom: 0.8rem;
      }

      @media (max-width: 480px) {
        font-size: 1.8rem;
      }
    }

    .section-subtitle {
      font-size: 1.3rem;
      margin: 0;
      opacity: 0.95;
      font-weight: 300;
      color: rgba(255, 255, 255, 0.9) !important;
      position: relative;
      z-index: 3;
      text-shadow: 0 2px 10px rgba(0,0,0,0.2);
      max-width: 600px;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1.1rem;
        max-width: 400px;
      }

      @media (max-width: 480px) {
        font-size: 1rem;
      }
    }
  }

  .inventory-content {
    padding: 0;
    position: relative;
    z-index: 2;

    .modern-tabs {
      ::ng-deep .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: none;
          background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
          padding: 0 2rem;
          box-shadow: 0 4px 30px rgba(102, 126, 234, 0.1);
          position: relative;
          border-radius: 0;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
          }

          .mat-mdc-tab {
            min-width: 200px;
            font-weight: 600;
            color: #4a5568;
            text-transform: none;
            font-size: 1.1rem;
            padding: 1.8rem 1.5rem;
            margin: 0 0.3rem;
            border-radius: 16px 16px 0 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(245, 87, 108, 0.08) 100%);
              opacity: 0;
              transition: all 0.4s ease;
            }

            &:hover {
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
              transform: translateY(-3px);
              color: #667eea;

              &::before {
                opacity: 1;
              }
            }

            &.mdc-tab--active {
              color: #667eea;
              background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
              box-shadow: 0 -6px 30px rgba(102, 126, 234, 0.2);
              transform: translateY(-6px);
              font-weight: 700;

              &::before {
                opacity: 1;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(245, 87, 108, 0.15) 100%);
              }
            }
          }

          .mat-ink-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f5576c 100%);
            height: 4px;
            border-radius: 2px;
            box-shadow: 0 2px 15px rgba(102, 126, 234, 0.4);
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
              background: white;
            }
          }
        }
      }
    }
  }
}

// Tab Content
.tab-content {
  padding: 4rem 3rem 3rem 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 50%, #edf2f7 100%);
  position: relative;
  min-height: 500px;

  // Add beautiful geometric pattern overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, rgba(245, 87, 108, 0.03) 1.5px, transparent 1.5px),
      linear-gradient(45deg, transparent 49%, rgba(102, 126, 234, 0.01) 50%, transparent 51%);
    background-size: 40px 40px, 60px 60px, 20px 20px;
    pointer-events: none;
    opacity: 0.7;
    animation: subtleFloat 20s ease-in-out infinite;
  }

  // Add a subtle top border with gradient
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    opacity: 0.6;
  }

  @media (max-width: 768px) {
    padding: 2.5rem 1.5rem 2rem 1.5rem;
    min-height: 350px;
  }

  @media (max-width: 480px) {
    padding: 2rem 1rem 1.5rem 1rem;
  }
}

// Series Overview
.series-overview {
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;

  .series-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    max-width: 700px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .series-stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.03);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // Add subtle gradient overlay
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
      transform: translate(30px, -30px);
      transition: all 0.3s ease;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 16px 48px rgba(0,0,0,0.15);

      &::before {
        transform: translate(20px, -20px) scale(1.2);
      }

      .stat-icon {
        transform: scale(1.1);
      }
    }

    .stat-icon {
      width: 64px;
      height: 64px;
      border-radius: 18px;
      background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;

      mat-icon {
        color: white;
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
      }
    }

    .stat-content {
      flex: 1;
      position: relative;
      z-index: 2;

      .stat-value {
        font-size: 1.75rem;
        font-weight: 800;
        color: #1a365d;
        line-height: 1;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .stat-label {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Modern Inventory Grid
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Modern Inventory Item Cards
.inventory-item {
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.03);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // Add beautiful gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #9C27B0 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // Add subtle background pattern
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, transparent 70%);
    transform: translate(50px, -50px);
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 24px 60px rgba(0,0,0,0.15);

    &::before {
      opacity: 1;
    }

    &::after {
      transform: translate(30px, -30px) scale(1.2);
    }
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;

    .denomination-info {
      display: flex;
      align-items: center;
      gap: 1.5rem;

      .denomination-icon {
        width: 100px;
        height: 60px;
        border-radius: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        border: 3px solid rgba(255,255,255,0.9);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05) rotate(2deg);
          box-shadow: 0 12px 32px rgba(0,0,0,0.15);
        }

        .note-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }

        .fallback-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
          color: #4CAF50;
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        // Add beautiful shimmer effect
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        // Add decorative corner accent
        &::after {
          content: '';
          position: absolute;
          top: 8px;
          right: 8px;
          width: 12px;
          height: 12px;
          background: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%);
          border-radius: 50%;
          opacity: 0.7;
        }
      }

      .denomination-details {
        flex: 1;

        .denomination-title {
          font-size: 1.5rem;
          font-weight: 800;
          color: #1a365d;
          margin: 0 0 0.5rem 0;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: -0.5px;
        }

        .denomination-series {
          font-size: 0.95rem;
          color: #718096;
          margin: 0 0 0.75rem 0;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .denomination-value {
          font-size: 0.875rem;
          font-weight: 700;
          color: #4CAF50;
          background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
          padding: 0.5rem 1rem;
          border-radius: 20px;
          display: inline-block;
          border: 2px solid rgba(76, 175, 80, 0.2);
          box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.25);
          }
        }
      }
    }

    .item-status {
      position: relative;
      z-index: 2;

      mat-chip {
        border-radius: 25px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }

        mat-icon {
          font-size: 1.1rem;
          width: 1.1rem;
          height: 1.1rem;
        }
      }
    }
  }

  .item-body {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;

    .item-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 2rem;

      .metric {
        text-align: center;
        padding: 1.5rem 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        border: 2px solid rgba(0,0,0,0.03);
        box-shadow: 0 8px 24px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #4CAF50 0%, #2196F3 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0,0,0,0.12);

          &::before {
            opacity: 1;
          }
        }

        .metric-value {
          font-size: 1.75rem;
          font-weight: 800;
          color: #1a365d;
          line-height: 1;
          margin-bottom: 0.5rem;
          background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .metric-label {
          font-size: 0.95rem;
          color: #718096;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    .item-actions {
      text-align: center;

      .add-btn {
        border-radius: 25px;
        font-weight: 700;
        text-transform: none;
        padding: 1rem 2rem;
        font-size: 0.95rem;
        box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
        background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
        border: none;
        color: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
          transition: left 0.5s ease;
        }

        &:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

          &::before {
            left: 100%;
          }
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  .stock-progress {
    position: relative;
    z-index: 2;
    margin-top: 1rem;

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      ::ng-deep .mat-mdc-progress-bar-buffer {
        background: rgba(0,0,0,0.05);
      }
    }

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.75rem;

      span {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

// Enhanced Status Chips
mat-chip {
  &.status-in-stock {
    background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    border: 2px solid rgba(76, 175, 80, 0.2);
  }

  &.status-watch {
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
    border: 2px solid rgba(33, 150, 243, 0.2);
  }

  &.status-medium {
    background: linear-gradient(135deg, #FFC107 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    border: 2px solid rgba(255, 193, 7, 0.2);
  }

  &.status-low {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
    border: 2px solid rgba(255, 152, 0, 0.2);
  }

  &.status-critical {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    border: 2px solid rgba(244, 67, 54, 0.2);
    animation: pulse 2s ease-in-out infinite;
  }

  &.status-unknown {
    background: linear-gradient(135deg, #718096 0%, #5a6c7d 100%) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4);
    border: 2px solid rgba(113, 128, 150, 0.2);
  }

  &.low-stock {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
    color: #d32f2f !important;
    border: 2px solid rgba(211, 47, 47, 0.2);
    box-shadow: 0 4px 16px rgba(211, 47, 47, 0.2);
  }

  &.normal-stock {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
    color: #388e3c !important;
    border: 2px solid rgba(56, 142, 60, 0.2);
    box-shadow: 0 4px 16px rgba(56, 142, 60, 0.2);
  }

  &.high-stock {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    color: #1976d2 !important;
    border: 2px solid rgba(25, 118, 210, 0.2);
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.2);
  }
}

// Modern No Data State
.no-data {
  text-align: center;
  padding: 5rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24px;
  margin: 3rem 0;
  border: 2px solid rgba(0,0,0,0.03);
  box-shadow: 0 12px 40px rgba(0,0,0,0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(33, 150, 243, 0.03) 0%, transparent 70%);
    pointer-events: none;
  }

  .no-data-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem auto;
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 32px rgba(33, 150, 243, 0.3);
    position: relative;
    z-index: 2;

    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
      border-radius: 50%;
      z-index: -1;
      animation: pulse 3s ease-in-out infinite;
    }

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: white;
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
    }
  }

  h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #1a365d;
    margin: 0 0 1rem 0;
    background: linear-gradient(135deg, #1a365d 0%, #2196F3 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 2;
  }

  p {
    font-size: 1.1rem;
    color: #718096;
    margin: 0 0 3rem 0;
    font-weight: 500;
    position: relative;
    z-index: 2;
  }

  button {
    border-radius: 25px;
    font-weight: 700;
    text-transform: none;
    padding: 1rem 2.5rem;
    font-size: 1rem;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
    background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
    border: none;
    color: white;
    position: relative;
    z-index: 2;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 12px 32px rgba(33, 150, 243, 0.4);

      &::before {
        left: 100%;
      }
    }
  }
}

// Series-Specific Styling
.inventory-item {
  &.mandela-series {
    border-left: 4px solid #4CAF50;

    .denomination-icon {
      border-color: rgba(76, 175, 80, 0.3);
    }
  }

  &.big5-series {
    border-left: 4px solid #FF9800;

    .denomination-icon {
      border-color: rgba(255, 152, 0, 0.3);
    }
  }

  &.commemorative-series {
    border-left: 4px solid #9C27B0;

    .denomination-icon {
      border-color: rgba(156, 39, 176, 0.3);
    }
  }

  &.v6-series {
    border-left: 4px solid #2196F3;

    .denomination-icon {
      border-color: rgba(33, 150, 243, 0.3);
    }
  }
}

// Enhanced fallback icon styling
.fallback-icon {
  display: none;
  color: #4CAF50;
  opacity: 0.7;
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// ===== REDESIGNED SECTION STYLES =====

// Enhanced Section Header
.section-header-redesigned {
  position: relative;
  margin-bottom: 3rem;
  border-radius: 32px;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  align-items: center;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #f5576c 75%,
        #4facfe 100%);
      opacity: 0.9;
    }

    .pattern-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
      background-size: 50px 50px;
      opacity: 0.3;
    }
  }

  .header-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 2rem 3rem;

    .header-text {
      .section-title-redesigned {
        font-size: 3rem;
        font-weight: 800;
        color: white;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);

        .title-icon {
          font-size: 3rem;
          width: 3rem;
          height: 3rem;
          background: rgba(255,255,255,0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
          font-size: 2rem;

          .title-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }
        }
      }

      .section-subtitle-redesigned {
        font-size: 1.25rem;
        color: rgba(255,255,255,0.9);
        margin: 0.5rem 0 0 0;
        font-weight: 500;
        text-shadow: 0 2px 10px rgba(0,0,0,0.2);

        @media (max-width: 768px) {
          font-size: 1rem;
        }
      }
    }

    .header-actions {
      .floating-action-btn {
        background: rgba(255,255,255,0.2);
        color: white;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255,255,255,0.3);
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          background: rgba(255,255,255,0.3);
          transform: translateY(-4px) scale(1.05);
          box-shadow: 0 12px 40px rgba(0,0,0,0.3);
        }

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
      padding: 1.5rem;
    }
  }
}

// Creative Tab Navigation
.series-navigation {
  margin-bottom: 2rem;

  .creative-tabs {
    .mat-mdc-tab-group {
      .mat-mdc-tab-header {
        border: none;
        background: transparent;

        .mat-mdc-tab-label-container {
          .mat-mdc-tab-list {
            .mat-mdc-tab {
              min-width: 200px;
              padding: 1rem 2rem;
              margin: 0 0.5rem;
              border-radius: 20px;
              background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
              border: 2px solid transparent;
              transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
              position: relative;
              overflow: hidden;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
                opacity: 0;
                transition: opacity 0.3s ease;
              }

              .tab-label-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                position: relative;
                z-index: 2;

                .tab-icon {
                  font-size: 1.25rem;
                  width: 1.25rem;
                  height: 1.25rem;
                  transition: all 0.3s ease;
                }

                .tab-text {
                  font-weight: 600;
                  font-size: 0.95rem;
                  transition: all 0.3s ease;
                }

                .tab-indicator {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  transition: all 0.3s ease;
                  opacity: 0;

                  &.indicator-mandela { background: #4CAF50; }
                  &.indicator-big_5 { background: #FF9800; }
                  &.indicator-commemorative { background: #9C27B0; }
                  &.indicator-v6 { background: #2196F3; }
                }
              }

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                border-color: rgba(102, 126, 234, 0.3);

                &::before {
                  opacity: 1;
                }

                .tab-label-content .tab-indicator {
                  opacity: 0.7;
                  transform: scale(1.2);
                }
              }

              &.mdc-tab--active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                transform: translateY(-4px);
                box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
                border-color: rgba(255,255,255,0.3);

                &::before {
                  opacity: 0;
                }

                .tab-label-content {
                  .tab-icon, .tab-text {
                    color: white;
                  }

                  .tab-indicator {
                    opacity: 1;
                    background: white;
                    transform: scale(1.3);
                    box-shadow: 0 0 10px rgba(255,255,255,0.5);
                  }
                }
              }
            }
          }

          .mat-ink-bar {
            display: none; // Hide default ink bar since we have custom styling
          }
        }
      }
    }
  }
}

// Enhanced Series Overview Cards
.series-overview-redesigned {
  margin-bottom: 3rem;

  .overview-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;

    @media (min-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .overview-card {
    position: relative;
    background: white;
    border-radius: 24px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .card-decoration {
      position: absolute;
      top: 0;
      right: 0;
      width: 120px;
      height: 120px;
      pointer-events: none;

      .decoration-circle {
        position: absolute;
        top: -30px;
        right: -30px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
        transition: all 0.3s ease;
      }

      .decoration-dots {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        background-image: radial-gradient(circle, rgba(102, 126, 234, 0.2) 2px, transparent 2px);
        background-size: 8px 8px;
        transition: all 0.3s ease;
      }
    }

    .card-icon-container {
      position: relative;
      z-index: 2;
      margin-bottom: 1.5rem;

      .icon-background {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;

        .card-icon {
          color: white;
          font-size: 1.75rem;
          width: 1.75rem;
          height: 1.75rem;
        }
      }
    }

    .card-content {
      position: relative;
      z-index: 2;

      .card-value {
        font-size: 2rem;
        font-weight: 800;
        color: #1a365d;
        margin-bottom: 0.5rem;
        line-height: 1;

        @media (max-width: 768px) {
          font-size: 1.75rem;
        }
      }

      .card-label {
        font-size: 0.875rem;
        font-weight: 700;
        color: #718096;
        letter-spacing: 0.5px;
        margin-bottom: 1rem;
      }

      .card-trend {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .trend-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
          color: #4CAF50;
        }

        .trend-text {
          font-size: 0.875rem;
          font-weight: 600;
          color: #4CAF50;
        }
      }

      .status-indicators {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          opacity: 0.3;
          transition: all 0.3s ease;

          &.good { background: #4CAF50; }
          &.warning { background: #FF9800; }
          &.critical { background: #f44336; }

          &.active {
            opacity: 1;
            transform: scale(1.2);
            box-shadow: 0 0 10px currentColor;
          }
        }
      }
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);

      .card-decoration {
        .decoration-circle {
          transform: scale(1.2) rotate(45deg);
          opacity: 0.8;
        }

        .decoration-dots {
          transform: rotate(45deg);
        }
      }

      .card-icon-container .icon-background {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
      }
    }

    // Series-specific styling
    &.card-mandela {
      border-left: 4px solid #4CAF50;

      .card-icon-container .icon-background {
        background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
      }
    }

    &.card-big_5 {
      border-left: 4px solid #FF9800;

      .card-icon-container .icon-background {
        background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
        box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
      }
    }

    &.card-commemorative {
      border-left: 4px solid #9C27B0;

      .card-icon-container .icon-background {
        background: linear-gradient(135deg, #9C27B0 0%, #7b1fa2 100%);
        box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
      }
    }

    &.card-v6 {
      border-left: 4px solid #2196F3;

      .card-icon-container .icon-background {
        background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
        box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
      }
    }
  }
}

// Redesigned Inventory Grid
.inventory-grid-redesigned {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2.5rem;

  @media (min-width: 1600px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .denomination-card {
    background: white;
    border-radius: 28px;
    padding: 0;
    box-shadow: 0 15px 50px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #9C27B0 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-12px) scale(1.02);
      box-shadow: 0 25px 70px rgba(0,0,0,0.15);

      &::before {
        opacity: 1;
      }
    }

    // Series-specific border colors
    &.series-mandela::before { background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%); }
    &.series-big_5::before { background: linear-gradient(90deg, #FF9800 0%, #FFB74D 100%); }
    &.series-commemorative::before { background: linear-gradient(90deg, #9C27B0 0%, #BA68C8 100%); }
    &.series-v6::before { background: linear-gradient(90deg, #2196F3 0%, #64B5F6 100%); }

    .card-header-redesigned {
      padding: 2rem 2rem 1rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 1.5rem;

      .note-display {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex: 1;

        .note-image-container {
          position: relative;
          width: 80px;
          height: 50px;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 8px 25px rgba(0,0,0,0.15);

          .note-image-redesigned {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.1) 0%, transparent 100%);
          }

          .denomination-badge {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 700;
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
          }

          &:hover .note-image-redesigned {
            transform: scale(1.1);
          }
        }

        .note-info {
          .note-title {
            font-size: 1.5rem;
            font-weight: 800;
            color: #1a365d;
            margin: 0 0 0.25rem 0;
            line-height: 1.2;
          }

          .note-series {
            font-size: 0.875rem;
            color: #718096;
            margin: 0 0 0.5rem 0;
            font-weight: 500;
          }

          .note-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: #667eea;
            margin: 0;
          }
        }
      }

      .status-section {
        .status-chip {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 0.875rem;
          font-weight: 600;
          transition: all 0.3s ease;

          .status-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }

          &.status-in-stock {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #2e7d32;
            border: 2px solid rgba(46, 125, 50, 0.2);
          }

          &.status-low {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #ef6c00;
            border: 2px solid rgba(239, 108, 0, 0.2);
          }

          &.status-critical {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
            border: 2px solid rgba(198, 40, 40, 0.2);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;

        .note-display {
          width: 100%;
        }

        .status-section {
          align-self: flex-start;
        }
      }
    }

    .metrics-section-redesigned {
      padding: 0 2rem 1.5rem 2rem;

      .metrics-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;

        .metric-card {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 16px;
          padding: 1.5rem;
          display: flex;
          align-items: center;
          gap: 1rem;
          transition: all 0.3s ease;
          border: 1px solid rgba(0,0,0,0.05);

          .metric-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

            mat-icon {
              font-size: 1.25rem;
              width: 1.25rem;
              height: 1.25rem;
            }
          }

          .metric-data {
            .metric-value-redesigned {
              font-size: 1.25rem;
              font-weight: 700;
              color: #1a365d;
              margin-bottom: 0.25rem;
              line-height: 1;
            }

            .metric-label-redesigned {
              font-size: 0.875rem;
              color: #718096;
              font-weight: 500;
            }
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
          }

          &.quantity-metric .metric-icon {
            background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
          }

          &.value-metric .metric-icon {
            background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
          }
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }
    }

    .stock-level-section {
      padding: 0 2rem 1.5rem 2rem;

      .stock-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .stock-label {
          font-size: 0.875rem;
          font-weight: 600;
          color: #718096;
        }

        .stock-percentage {
          font-size: 1rem;
          font-weight: 700;
          color: #1a365d;
        }
      }

      .stock-progress-redesigned {
        .progress-track {
          width: 100%;
          height: 8px;
          background: #e2e8f0;
          border-radius: 4px;
          overflow: hidden;
          position: relative;

          .progress-fill {
            height: 100%;
            border-radius: 4px;
            position: relative;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

            .progress-glow {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border-radius: 4px;
              box-shadow: 0 0 10px currentColor;
              opacity: 0.5;
            }

            &.primary {
              background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
            }

            &.warn {
              background: linear-gradient(90deg, #FF9800 0%, #FFB74D 100%);
            }

            &.accent {
              background: linear-gradient(90deg, #2196F3 0%, #64B5F6 100%);
            }
          }
        }

        .stock-indicators {
          display: flex;
          justify-content: space-between;
          margin-top: 0.75rem;

          .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            opacity: 0.3;
            transition: all 0.3s ease;

            &.low { background: #f44336; }
            &.medium { background: #FF9800; }
            &.high { background: #4CAF50; }

            &.active {
              opacity: 1;
              transform: scale(1.2);
              box-shadow: 0 0 10px currentColor;
            }
          }
        }
      }
    }

    .card-actions-redesigned {
      padding: 1.5rem 2rem 2rem 2rem;
      display: flex;
      gap: 1rem;

      .action-btn {
        flex: 1;
        padding: 0.75rem 1.5rem;
        border-radius: 16px;
        font-weight: 600;
        font-size: 0.875rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        mat-icon {
          font-size: 1.125rem;
          width: 1.125rem;
          height: 1.125rem;
        }

        &.primary-action {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
          }
        }

        &.secondary-action {
          background: transparent;
          color: #667eea;
          border: 2px solid #667eea;

          &:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
          }
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.75rem;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}


